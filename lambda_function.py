import json
import asyncio

from aws_lambda_powertools import <PERSON><PERSON>
from aws_lambda_powertools.utilities.parser import event_parser  # noqa
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import ValidationError

from parser.types import InputData
from parser.utils import (
    extract_availability_from_body,
    extract_availability_from_subject,
    create_error_response,
)

logger = Logger()


@logger.inject_lambda_context
def handler(event, context: LambdaContext):
    logger.info(event)

    async def async_handler():
        try:
            json_content = json.loads(event["body"])
            input_data = InputData.model_validate(json_content)

            # skip and return 200 if subject does not contain `HAVE` keyword
            if "HAVE" not in input_data.subject:
                return create_error_response("Unrecognized subject (missing HAVE keyword)", 200)

            availability_info = await extract_availability_from_body(
                input_data.body
            )

            if availability_info.availability_window[0] is None:
                result = await extract_availability_from_subject(
                    f"Email date: {input_data.date}, " + input_data.subject
                )

            # If transient, set to_airport_code to None
            if availability_info.availability_type == "transient":
                availability_info.to_airport_code = None

            return availability_info

        except ValidationError as e:
            logger.error(f"Validation error: {str(e)}")
            return create_error_response(f"Validation error: {str(e)}", 422)

        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return create_error_response(f"Internal server error: {str(e)}", 500)

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        data = loop.run_until_complete(async_handler())
        logger.info(data.model_dump())
        return {
            "statusCode": 200,
            "body": data.model_dump_json(),
        }
    finally:
        loop.close()
