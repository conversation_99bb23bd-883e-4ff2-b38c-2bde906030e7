from datetime import date
from typing import Literal, Annotated

from pydantic import Field, BaseModel

AvailabilityType = Literal["transient", "oneway", "roundtrip"]
AirportIATACode = Annotated[str, Field(pattern=r"^[a-zA-Z]{3}$")]
AirportICAOCode = Annotated[str, Field(pattern=r"^[a-zA-Z]{4}$")]


class AvailabilityInfo(BaseModel):
    availability_type: AvailabilityType
    availability_window: Annotated[
        tuple[date, date | None],
        Field(
            description="Start and end date of availability. If end date is not provided, it is a one-way trip."
        ),
    ]
    aircraft_model: str
    seats: int | None
    from_airport_code: AirportICAOCode | AirportIATACode
    to_airport_code: AirportICAOCode | AirportIATACode | None
    operator_name: str
    operator_email: str
    operator_phone: str
    operator_address: str
    sent_via: Annotated[
        str,
        Field(
            description="Powered by or sent via caption if present in the email footer"
        ),
    ]  # sent from (e.g. FlyEasy, CharterPad, etc.)


class InputData(BaseModel):
    date: Annotated[str | date, Field(alias="Date")]
    subject: Annotated[str, Field(alias="Subject")]
    body: Annotated[str, Field(alias="TextBody")]
