import json
from datetime import datetime
from typing import Any

from bs4 import BeautifulSoup

from .agent import agent
from .types import AvailabilityInfo


def clean_html(html_content: str) -> str:
    doctype_index = html_content.find("<!doctype html>")
    if doctype_index != -1:
        return html_content[doctype_index:]
    else:
        raise RuntimeError("Unable to cleanup html content")


async def extract_availability_from_body(
    body: str, html: bool = False
) -> AvailabilityInfo:
    """
    Extract availability details from a charter availability email HTML or Text Body
    """

    if html:
        content = body.replace("=3D", "").replace("=20", "").replace("=09", "")
        soup = BeautifulSoup(content, "html.parser")

        # Use the AI agent to parse the text into our schema
        result = await agent.run(soup.get_text(" ", strip=True))
    else:
        result = await agent.run(body)

    return result.output


async def extract_availability_from_subject(subject: str) -> AvailabilityInfo:
    """
    Extract availability details from a charter availability email subject
    """
    # Use the AI agent to parse the text into our schema
    result = await agent.run(subject)
    return result.output


def create_error_response(error_message: str, status_code: int = 400) -> dict[str, Any]:
    """Create standardized error response"""
    return {
        "statusCode": status_code,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
        },
        "body": json.dumps(
            {
                "error": error_message,
                "timestamp": datetime.utcnow().isoformat(),  # noqa
            }
        ),
    }
