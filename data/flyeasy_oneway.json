{"FromName": "", "MessageStream": "inbound", "From": "<EMAIL>", "FromFull": {"Email": "<EMAIL>", "Name": "", "MailboxHash": ""}, "To": "<EMAIL>", "ToFull": [{"Email": "<EMAIL>", "Name": "", "MailboxHash": ""}], "Cc": "", "CcFull": [], "Bcc": "", "BccFull": [], "OriginalRecipient": "<EMAIL>", "Subject": "HAVE: Cessna Citation CJ3+(6 seats) Empty KASE - KLAS on Aug 10 2025   [request id:R6D3SQ34U]", "MessageID": "41cff246-570e-4100-b7b1-ffa6fad4684b", "ReplyTo": "<EMAIL>", "MailboxHash": "", "Date": "<PERSON><PERSON>, 5 Aug 2025 20:05:41 +0000", "TextBody": " From locationCharter Airmail\n\n\n One-way AvailableAug 10 2025 (KASE) Aspen, United States - (KLAS) Las Vegas, United States\nCessna Citation CJ3+\n (6 seats)\nFrom: (KASE) Aspen, Colorado, United States - Aspen-Pitkin Co/Sardy Field\nAug 10 2025\n\nTo: (KLAS) Las Vegas, Nevada, United States - McCarran International Airport\n<NAME_EMAIL> or call ************\n                Thrive Aviation\n<EMAIL>\n                ************\n                1410 Jet Stream Drive #200 Henderson, NV 89052, USA            \nPowered by FlyEasy https://www.flyeasy.co\n Reply\n\n\n[_fa_id:R6D3SQ34U~57da0aa2c1fe036a3c70e87d:_]\n  \n        \t\t\t\t\t\t\t\t\t\t\t\t\tYou are subscribed to Charter Availability. To unsubscribe please click here and select \"Leave the group\" \n        \t\t\t\t\t\t\t\t\t\t\t\tAir Mail Sponsor\n\n \n\n                                                    \nNational Business Aviation Association\n1200 G Street NW, Suite 1100, Washington, DC 20005\n\nTel: ************  |  <EMAIL>  |  nbaa.org", "HtmlBody": "<smartmail-template type=\"handlebars\" />\n\n<!doctype html>\n<html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:v=\"urn:schemas-microsoft-com:vml\">\n<head>\n    <!-- NAME: ANNOUNCE -->\n    <!--[if gte mso 15]>\n    <xml>\n        <o:OfficeDocumentSettings>\n        <o:AllowPNG/>\n        <o:PixelsPerInch>96</o:PixelsPerInch>\n        </o:OfficeDocumentSettings>\n    </xml>\n    <![endif]-->\n    <meta charset=\"UTF-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title></title>\n    <style type=\"text/css\">\n        p {\n            margin: 10px 0;\n            padding: 0;\n        }\n\n        table {\n            border-collapse: collapse;\n        }\n\n        h1, h2, h3, h4, h5, h6 {\n            display: block;\n            margin: 0;\n            padding: 0;\n        }\n\n        img, a img {\n            border: 0;\n            height: auto;\n            outline: none;\n            text-decoration: none;\n        }\n\n        body, #body-table, #body-cell {\n            height: 100%;\n            margin: 0;\n            padding: 0;\n            width: 100%;\n        }\n\n        #outlook a {\n            padding: 0;\n        }\n\n        img {\n            -ms-interpolation-mode: bicubic;\n        }\n\n        table {\n            mso-table-lspace: 0pt;\n            mso-table-rspace: 0pt;\n        }\n\n        .ReadMsgBody {\n            width: 100%;\n        }\n\n        .ExternalClass {\n            width: 100%;\n        }\n\n        p, a, li, td, blockquote {\n            mso-line-height-rule: exactly;\n        }\n\n            a[href^=tel], a[href^=sms] {\n                color: inherit;\n                cursor: default;\n                text-decoration: none;\n            }\n\n        p, a, li, td, body, table, blockquote {\n            -ms-text-size-adjust: 100%;\n            -webkit-text-size-adjust: 100%;\n        }\n\n        .ExternalClass, .ExternalClass p, .ExternalClass td, .ExternalClass div, .ExternalClass span, .ExternalClass font {\n            line-height: 100%;\n        }\n\n        a[x-apple-data-detectors] {\n            color: inherit !important;\n            text-decoration: none !important;\n            font-size: inherit !important;\n            font-family: inherit !important;\n            font-weight: inherit !important;\n            line-height: inherit !important;\n        }\n\n        .template-container {\n            max-width: 600px !important;\n        }\n\n        a.call-to-action-button {\n            display: block;\n        }\n\n        .image, .mcnRetinaImage {\n            vertical-align: bottom;\n        }\n\n        .content-bl-content {\n            word-break: break-word;\n        }\n\n            .content-bl-content img {\n                height: auto !important;\n            }\n\n        .divider-table {\n            table-layout: fixed !important;\n        }\n\n        h1 {\n            color: #222222;\n            font-family: Helvetica;\n            font-size: 40px;\n            font-style: normal;\n            font-weight: bold;\n            line-height: 150%;\n            letter-spacing: normal;\n            text-align: center;\n        }\n\n        h2 {\n            color: #222222;\n            font-family: Helvetica;\n            font-size: 34px;\n            font-style: normal;\n            font-weight: bold;\n            line-height: 150%;\n            letter-spacing: normal;\n            text-align: left;\n        }\n\n        h3 {\n            color: #444444;\n            font-family: Helvetica;\n            font-size: 22px;\n            font-style: normal;\n            font-weight: bold;\n            line-height: 150%;\n            letter-spacing: normal;\n            text-align: left;\n        }\n\n        h4 {\n            color: #949494;\n            font-family: Georgia;\n            font-size: 20px;\n            font-style: italic;\n            font-weight: normal;\n            line-height: 125%;\n            letter-spacing: normal;\n            text-align: center;\n        }\n\n        #template-header {\n            background-color: #F7F7F7;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 54px;\n            padding-bottom: 54px;\n        }\n\n        .header-container {\n            background-color: transparent;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 0;\n            padding-bottom: 0;\n        }\n\n            .header-container .content-bl-content, .header-container .content-bl-content p {\n                color: #757575;\n                font-family: Helvetica;\n                font-size: 16px;\n                line-height: 150%;\n                text-align: left;\n            }\n\n                .header-container .content-bl-content a, .header-container .content-bl-content p a {\n                    color: #007C89;\n                    font-weight: normal;\n                    text-decoration: underline;\n                }\n\n        #template-body {\n            background-color: #FFFFFF;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 36px;\n            padding-bottom: 54px;\n        }\n\n        .body-container {\n            background-color: transparent;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 0;\n            padding-bottom: 0;\n        }\n\n            .body-container .content-bl-content, .body-container .content-bl-content p {\n                color: #757575;\n                font-family: Helvetica;\n                font-size: 16px;\n                line-height: 150%;\n                text-align: left;\n            }\n\n                .body-container .content-bl-content a, .body-container .content-bl-content p a {\n                    color: #007C89;\n                    font-weight: normal;\n                    text-decoration: underline;\n                }\n\n        #footer {\n            background-color: #001e60;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 45px;\n            padding-bottom: 63px;\n        }\n\n        .footer-container {\n            background-color: transparent;\n            background-image: none;\n            background-repeat: no-repeat;\n            background-position: center;\n            background-size: cover;\n            border-top: 0;\n            border-bottom: 0;\n            padding-top: 0;\n            padding-bottom: 0;\n        }\n\n            .footer-container .content-bl-content, .footer-container .content-bl-content p {\n                color: #FFFFFF;\n                font-family: Helvetica;\n                font-size: 12px;\n                line-height: 150%;\n                text-align: center;\n            }\n\n                .footer-container .content-bl-content a, .footer-container .content-bl-content p a {\n                    color: #FFFFFF;\n                    font-weight: normal;\n                    text-decoration: underline;\n                }\n\n        @media only screen and (min-width:768px) {\n            .template-container {\n                width: 600px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            body, table, td, p, a, li, blockquote {\n                -webkit-text-size-adjust: none !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            body {\n                width: 100% !important;\n                min-width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .mcnRetinaImage {\n                max-width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .image {\n                width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .mcnCartContainer, .mcnCaptionTopContent, .mcnRecContentContainer, .mcnCaptionBottomContent, .content-bl-wrap, .mcnBoxedTextContentContainer, .imageGroupContentContainer, .mcnCaptionLeftTextContentContainer, .mcnCaptionRightTextContentContainer, .mcnCaptionLeftImageContentContainer, .mcnCaptionRightImageContentContainer, .imageCardLeftTextContentContainer, .imageCardRightTextContentContainer, .imageCardLeftImageContentContainer, .imageCardRightImageContentContainer {\n                max-width: 100% !important;\n                width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .mcnBoxedTextContentContainer {\n                min-width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageGroupContent {\n                padding: 9px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .mcnCaptionLeftContentOuter .content-bl-content, .mcnCaptionRightContentOuter .content-bl-content {\n                padding-top: 9px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageCardTopImageContent, .mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent, .mcnCaptionBlockInner .mcnCaptionTopContent:last-child .content-bl-content {\n                padding-top: 18px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageCardBottomImageContent {\n                padding-bottom: 9px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageGroupBlockInner {\n                padding-top: 0 !important;\n                padding-bottom: 0 !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageGroupBlockOuter {\n                padding-top: 9px !important;\n                padding-bottom: 9px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .content-bl-content, .mcnBoxedTextContentColumn {\n                padding-right: 18px !important;\n                padding-left: 18px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .imageCardLeftImageContent, .imageCardRightImageContent {\n                padding-right: 18px !important;\n                padding-bottom: 0 !important;\n                padding-left: 18px !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .mcpreview-image-uploader {\n                display: none !important;\n                width: 100% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            h1 {\n                font-size: 30px !important;\n                line-height: 125% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            h2 {\n                font-size: 26px !important;\n                line-height: 125% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            h3 {\n                font-size: 20px !important;\n                line-height: 150% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            h4 {\n                font-size: 18px !important;\n                line-height: 150% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            .mcnBoxedTextContentContainer .content-bl-content, .mcnBoxedTextContentContainer .content-bl-content p {\n                font-size: 14px !important;\n                line-height: 150% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            .header-container .content-bl-content, .header-container .content-bl-content p {\n                font-size: 16px !important;\n                line-height: 150% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n\n            .body-container .content-bl-content, .body-container .content-bl-content p {\n                font-size: 16px !important;\n                line-height: 150% !important;\n            }\n        }\n\n        @media only screen and (max-width: 480px) {\n            .footer-container .content-bl-content, .footer-container .content-bl-content p {\n                font-size: 14px !important;\n                line-height: 150% !important;\n            }\n        }\n        \t\n\n        \n    </style>\n</head>\n<body class=\"vsc-initialized\" data-gr-ext-installed=\"\" data-new-gr-c-s-check-loaded=\"14.1022.0\" data-new-gr-c-s-loaded=\"14.1006.0\">\n    <center>\n        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" height=\"100%\" id=\"body-table\" width=\"100%\">\n            <tbody>\n                <tr>\n                    <td align=\"center\" id=\"body-cell\" valign=\"top\">\n                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                            <!--LOGO ROW-->\n                            <tbody>\n                                <tr>\n                                    <td align=\"center\" data-template-container=\"\" id=\"template-header\" valign=\"top\">\n                                        <!--[if (gte mso 9)|(IE)]>\n                                        <table align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"width:600px;\">\n                                        <tr>\n                                        <td align=\"center\" valign=\"top\" width=\"600\" style=\"width:600px;\">\n                                        <![endif]-->\n                                        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"template-container\" width=\"100%\">\n                                            <tbody>\n                                                <tr>\n                                                    <td class=\"header-container\" valign=\"top\">\n                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"image-block\" style=\"min-width:100%;\" width=\"100%\">\n                                                            <tbody class=\"image-block-ext\">\n                                                                <tr>\n                                                                    <td class=\"image-block-int\" style=\"padding:9px\" valign=\"top\">\n                                                                        <table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"image-content-wrap\" style=\"min-width:100%;\" width=\"100%\">\n                                                                            <tbody>\n                                                                                <tr>\n                                                                                    <td class=\"image-content\" style=\"padding-right: 9px; padding-left: 9px; padding-top: 0; padding-bottom: 0; text-align:center;\" valign=\"top\"><img align=\"middle\" alt=\"\" class=\"image\" src=\"https://connect.nbaa.org/images/emailtemplate/logo.png\" style=\"max-width: 100%; padding-bottom: 0px; vertical-align: bottom; display: inline !important; width: 196px;\" width=\"196\" /></td>\n                                                                                </tr>\n                                                                            </tbody>\n                                                                        </table>\n                                                                    </td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                    </td>\n                                                </tr>\n                                            </tbody>\n                                        </table>\n                                        <!--[if (gte mso 9)|(IE)]>\n                                                                </td>\n                                                                </tr>\n                                                                </table>\n                                                                <![endif]-->\n                                    </td>\n                                </tr>\n                                <!--CONTENT ROW-->\n                                <tr>\n                                    <td align=\"center\" data-template-container=\"\" id=\"template-body\" valign=\"top\">\n                                        <!--[if (gte mso 9)|(IE)]>\n                                        <table align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"width:600px;\">\n                                        <tr>\n                                        <td align=\"center\" valign=\"top\" width=\"600\" style=\"width:600px;\">\n                                        <![endif]-->\n                                        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"template-container\" width=\"100%\">\n                                            <tbody>\n                                                <tr>\n                                                    <td class=\"body-container\" valign=\"top\">\n                                                        <!--DIVIDER-->\n                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-table\" style=\"min-width:100%;\" width=\"100%\">\n                                                            <tbody class=\"divider-tbody\">\n                                                                <tr>\n                                                                    <td class=\"divider-td\" style=\"min-width: 100%; padding: 9px 18px 0px;\">\n                                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-content\" style=\"min-width:100%;\" width=\"100%\">\n                                                                            <tbody>\n                                                                                <tr>\n                                                                                    <td>&nbsp;</td>\n                                                                                </tr>\n                                                                            </tbody>\n                                                                        </table>\n                                                                    </td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                        <!--Text area-->\n\n                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"content-block\" style=\"min-width:100%;\" width=\"100%\">\n                                                            <tbody class=\"content-bl-body\">\n                                                                <tr>\n                                                                    <td class=\"content-bl-td\" style=\"padding-top:9px;\" valign=\"top\">\n                                                                        <!--[if mso]>\n                                                                        <table align=\"left\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"width:100%;\">\n                                                                        <tr>\n                                                                        <![endif]-->\n                                                                        <!--[if mso]>\n                                                                        <td valign=\"top\" width=\"600\" style=\"width:600px;\">\n                                                                        <![endif]-->\n                                                                        <table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"content-bl-wrap\" style=\"max-width:100%; min-width:100%;\" width=\"100%\">\n                                                                            <tbody>\n                                                                                <tr>\n                                                                                    <td class=\"content-bl-content\" style=\"padding-top:0; padding-right:18px; padding-bottom:9px; padding-left:18px;\" valign=\"top\">\n                                                                                        <table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"content-bl-wrap\" style=\"max-width:100%; min-width:100%;\" width=\"100%\">\n                                                                                            <tbody>\n                                                                                                <tr>\n                                                                                                    <td class=\"content-bl-content\" style=\"padding-top:0; padding-right:5px; padding-bottom:5px; padding-left:5px;\" valign=\"top\"><b>From location</b></td>\n                                                                                                    <td class=\"content-bl-content\" style=\"padding-top:0; padding-right:5px; padding-bottom:5px; padding-left:5px;\" valign=\"top\">Charter Airmail</td>\n                                                                                                </tr>\n                                                                                               <tr>\n                                                                                                    <td class=\"content-bl-content\" colspan=\"2\" style=\"padding-top:0; padding-right:18px; padding-bottom:9px; padding-left:18px;\" valign=\"top\">\n                                                                                                        <p>        <div style=\"padding:10px;\">            <p><!--<img src=\"https://flyeasy.s3.amazonaws.com/logos/57da0aa2c1fe036a3c70e87d.jpg\" height=\"50\" style=\"height:50px;\"/>--></p>                                    <p style=\"color: #ff003c;font-size:18px;margin-top:20px;margin-bottom:20px;\"><b> One-way Available</b></p>                        <table cellspacing=\"0\" style=\"margin:4px;margin-bottom:20px;border:1px solid silver;border-radius:8px;width:100%;\"><tr style=\"margin:0;padding:0;\"><td style=\"background:#eeeeee; color: #ff003c;border-top-left-radius:8px;border-top-right-radius:8px;border-bottom:1px solid silver;font-weight:bold;\" colspan=\"3\"><div style=\"padding:8px;\">Aug 10 2025 (KASE) Aspen, United States - (KLAS) Las Vegas, United States</div></td></tr><tr><td valign=\"top\" width=\"20%\"><div style=\"padding:8px;\"><p>Cessna Citation CJ3+<br> (6 seats)</p><img src=\"https://flyeasy.s3.amazonaws.com/fleets/min/9969d10f8ef9f855fecd0db14b3952bb_1542846626705_01553537306377_fe_opt_.jpg\" width=\"140\"></div></td><td valign=\"top\" width=\"40%\"><div style=\"padding:8px;\"><p>From: (KASE) Aspen, Colorado, United States - Aspen-Pitkin Co/Sardy Field</p><p>Aug 10 2025</p><p style=\"font-size:16px;\"></p></div></td><td valign=\"top\" width=\"40%\"><div style=\"padding:8px;\"><p>To: (KLAS) Las Vegas, Nevada, United States - McCarran International Airport</p></div></td></tr></table>            <p>Reply to <a href=\"mailto:<EMAIL>\" style=\"text-decoration:none;\"><EMAIL></a> or call ************</p>                        <p>                Thrive Aviation<br>           <a href=\"mailto:<EMAIL>\" style=\"text-decoration:none;\"><EMAIL></a><br>                ************<br>                1410 Jet Stream Drive #200 Henderson, NV 89052, USA            </p>                    </div>                        <p style=\"font-size:14px;color:#444;text-align:right;\">Powered by FlyEasy https://www.flyeasy.co</p>    \n\n<br>\n                <!-- Reply button -->\n                <table width=\"120\" align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;width:120px;\">\n                    <tr>\n                        <td valign=\"top\" align=\"center\" style=\"mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color:#eee;padding:8px;text-align:center;background:#ff003c;padding:10px;border-radius:8px;\">\n                            <a href=\"mailto:<EMAIL>?subject=RE:HAVE: Cessna Citation CJ3+(6 seats) Empty KASE - KLAS on Aug 10 2025   [request id:R6D3SQ34U]&cc=<EMAIL>\" style=\"color:#ffffff;text-align:center;cursor:pointer;text-decoration:none;\" target=\"_blank\"><img src=\"https://flyeasy.s3.amazonaws.com/website/admin/airmail/reply24.png\" width=\"12\" height=\"12\"><font color=\"#ffffff\"> Reply</font></a>\n                        </td>\n                    </tr>\n                </table>\n                <!-- End of Reply button --><br><br>\n\n<br><i><font color=\"#eeeeee\">[_fa_id:R6D3SQ34U~57da0aa2c1fe036a3c70e87d:_]</font></i><br></p>\n                                                                                                    </td>\n                                                                                                </tr>\n                                                                                            </tbody>\n                                                                                        </table>\n                                                                                    </td>\n                                                                                </tr>\n                                                                            </tbody>\n                                                                        </table>\n                                                                        <!--[if mso]>\n                                                                                                            </td>\n                                                                                                            <![endif]-->\n                                                                        <!--[if mso]>\n                                                                        </tr>\n                                                                        </table>\n                                                                        <![endif]-->\n                                                                    </td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                        <!--DIVIDER-->\n\n                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-table\" style=\"min-width:100%;\" width=\"100%\">\n                                                            <tbody class=\"divider-tbody\">\n                                                                <tr>\n                                                                    <td class=\"divider-td\" style=\"min-width: 100%; padding: 9px 18px;\">\n                                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-content\" style=\"min-width: 100%;border-top: 1px solid #E0E0E0;\" width=\"100%\">\n                                                                            <tbody>\n                                                                                <tr>\n                                                                                    <td>&nbsp;</td>\n                                                                                </tr>\n                                                                            </tbody>\n                                                                        </table>\n                                                                    </td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                        <!--DIVIDER-->\n\n                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-table\" style=\"min-width:100%;\" width=\"100%\">\n                                                            <tbody class=\"divider-tbody\">\n                                                                <tr>\n                                                                    <td class=\"divider-td\" style=\"min-width: 100%; padding: 18px 18px 0px;\">\n                                                                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"divider-content\" style=\"min-width:100%;\" width=\"100%\">\n                                                                            <tbody>\n                                                                                <tr>\n                                                                                    <td>&nbsp;</td>\n                                                                                </tr>\n                                                                            </tbody>\n                                                                        </table>\n                                                                    </td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                    </td>\n                                                </tr>\n                                            </tbody>\n                                        </table>\n                                        <!--[if (gte mso 9)|(IE)]>\n                                                                </td>\n                                                                </tr>\n                                                                </table>\n                                                                <![endif]-->\n                                    </td>\n                                </tr>\n                                \n                                <tr>\n        \t\t\t\t\t\t\t<td align=\"center\" valign=\"top\" data-template-container style=\"background:#f7f7f7;\">\n        \t\t\t\t\t\t\t\t<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" class=\"template-container\">\n        \t\t\t\t\t\t\t\t\t<tbody>\n        \t\t\t\t\t\t\t\t\t\t<tr>\n        \t\t\t\t\t\t\t\t\t\t\t<td style=\"padding:18px;\" align=\"center\">\n        \t\t\t\t\t\t\t\t\t\t\t\t<div style=\"font-size: 10px; font-size: 9pt; color:#404040; line-height:1.6; font-family: Helvetica;\">\n        \t\t\t\t\t\t\t\t\t\t\t\t\tYou are subscribed to Charter Availability. To unsubscribe please <a href=\"https://connect.nbaa.org/549/Charter-Availability?id=549&tab=discussion#viewTab\">click here</a> and select \"Leave the group\" \n        \t\t\t\t\t\t\t\t\t\t\t\t</div>\n        \t\t\t\t\t\t\t\t\t\t\t</td>\n        \t\t\t\t\t\t\t\t\t\t</tr>\n        \t\t\t\t\t\t\t\t\t</tbody>\n        \t\t\t\t\t\t\t\t</table>\n        \t\t\t\t\t\t\t</td>\n        \t\t\t\t\t\t</tr>\n                                 <tr>\n        \t\t\t\t\t\t\t<td align=\"center\" valign=\"top\" data-template-container style=\"background:#f7f7f7;\">\n        \t\t\t\t\t\t\t\t<table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" class=\"template-container\">\n        \t\t\t\t\t\t\t\t\t<tbody>\n                                                <tr>\n                                                    <td align=\"center\">\n                                                        <table style=\"font-family: Arial, Helvetica, sans-serif; -webkit-font-smoothing: antialiased; font-weight: 400; font-size: 15.5px; line-height: 140%; width: 578px;\">\n<tbody>\n<tr>\n<td style=\"border: 1px solid #8e8e8e; background-color: #ededed\">\n<h3 style=\"color: #8e8e8e;font-size: 13px; margin:5px 14px; font-weight: 100\">Air Mail Sponsor</h3>\n<p align=\"center\" style=\"margin:5px 5px 10px 5px\"><a href=\"https://nbaa.org/airmail/CE25A131\"><img src=\"https://files.rackspace9.diamax.com/nbaa/filestorage/584455/\" alt=\"2025 NBAA-BACE - Secure Your Spot\" width=\"97%\" height=\"auto\"></a> </p>\n</td>\n</tr>\n</tbody>\n</table>\n&nbsp;\n\n                                                    </td>\n                                                </tr>\n        \t\t\t\t\t\t\t\t\t</tbody>\n        \t\t\t\t\t\t\t\t</table>\n        \t\t\t\t\t\t\t</td>\n        \t\t\t\t\t\t</tr>\n                                <!--FOOTER ROW-->\n                            <!-----FOOTER --------->\t\n<tr>\n<td style=\"border-top: 7px solid #7da0c4\"  bgcolor=\"#F2F2F2\">\n\n<p class=\"tiny\" style=\"text-align: center; color:#3b3b3b; font-weight:400; font-size:13px; line-height: 140%; margin:  20px;font-family: Arial, Helvetica, sans-serif;\"><b>National Business Aviation Association</b><br>1200 G Street NW, Suite 1100, Washington, DC 20005\n<br>Tel: ************&nbsp; |&nbsp; <a alias=\"<EMAIL>\" conversion=\"false\" data-linkto=\"mailto:\" href=\"mailto:<EMAIL>\" style=\"color:#575147;text-decoration:underline;font-weight:400;line-height:100%;\" title=\"<EMAIL>\"><EMAIL></a>&nbsp; |&nbsp; <a alias=\"<EMAIL>\" conversion=\"false\" data-linkto=\"mailto:\" href=\"https://nbaa.org/\" style=\"color:#575147;text-decoration:underline;font-weight:400;line-height:100%;\" title=\"<EMAIL>\">nbaa.org</a></p>\n\t\n</td>\n</tr>\n                            </tbody>\n                        </table>\n                    </td>\n                </tr>\n            </tbody>\n        </table>\n    </center>\n\t</body>\n</html>", "StrippedTextReply": "", "Tag": "", "Headers": [{"Name": "Return-Path", "Value": "<<EMAIL>>"}, {"Name": "Received", "Value": "by p-pm-inboundg01a-aws-useast1a.inbound.postmarkapp.com (Postfix, from userid 996)\tid 1357740503B; Tue,  5 Aug 2025 20:05:42 +0000 (UTC)"}, {"Name": "X-Spam-Checker-Version", "Value": "SpamAssassin 3.4.0 (2014-02-07) on\tp-pm-inboundg01a-aws-useast1a"}, {"Name": "X-Spam-Status", "Value": "No"}, {"Name": "X-Spam-Score", "Value": "-0.1"}, {"Name": "X-Spam-Tests", "Value": "DKIM_SIGNED,DKIM_VALID,DKIM_VALID_AU,HTML_FONT_LOW_CONTRAST,\tHTML_IMAGE_RATIO_06,HTML_MESSAGE,<PERSON><PERSON>_IN_DNSWL_BLOCKED,<PERSON><PERSON>_IN_MSPIKE_H5,\t<PERSON><PERSON>_IN_MSPIKE_WL,<PERSON><PERSON>_IN_VALIDITY_RPBL_BLOCKED,<PERSON><PERSON>_IN_VALIDITY_SAFE_BLOCKED,\t<PERSON><PERSON>_IN_ZEN_BLOCKED_OPENDNS,SPF_HELO_NONE,SPF_PASS,URIBL_DBL_BLOCKED_OPENDNS,\tURIBL_ZEN_BLOCKED_OPENDNS"}, {"Name": "Received-SPF", "Value": "pass (amazonses.com: *********** is authorized to use '<EMAIL>' in 'mfrom' identity (mechanism 'ip4:**********/18' matched)) receiver=p-pm-inboundg01a-aws-useast1a; identity=mailfrom; envelope-from=\"<EMAIL>\"; helo=a9-26.smtp-out.amazonses.com; client-ip=***********"}, {"Name": "Received", "Value": "from a9-26.smtp-out.amazonses.com (a9-26.smtp-out.amazonses.com [***********])\t(using TLSv1.2 with cipher ECDHE-RSA-AES128-SHA256 (128/128 bits))\t(No client certificate requested)\tby p-pm-inboundg01a-aws-useast1a.inbound.postmarkapp.com (Postfix) with ESMTPS id A8261440720\tfor <<EMAIL>>; Tue,  5 Aug 2025 20:05:41 +0000 (UTC)"}, {"Name": "DKIM-Signature", "Value": "v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple;\ts=leauiacotfaswkg32xq4kc3dhafry7vx; d=airmail.nbaa.org;\tt=**********;\th=Date:From:Reply-To:To:Subject:MIME-Version:Content-Type:Message-ID;\tbh=7U3n+gcaNHSfHe/OGpiqG7621aR6dp/AjG0cOHkyj3c=;\tb=TXmxRl6MBX8gg00qI7WOv4q221TsBkz+2Jj6psZW890F6RylVLdlb4uT/XgFJ8/C\t9fo2XblVWaNcs1uetWrCbJMjI1sx9c5blNHJ3Ahahq1g3Gw8b/8JmLa5j+ctHB8gmXz\tOil+9bdAZYmSwJ25mSENllwxmn6UhePWVB2V7LzY="}, {"Name": "DKIM-Signature", "Value": "v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple;\ts=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw; d=amazonses.com; t=**********;\th=Date:From:Reply-To:To:Subject:MIME-Version:Content-Type:Message-ID:Feedback-ID;\tbh=7U3n+gcaNHSfHe/OGpiqG7621aR6dp/AjG0cOHkyj3c=;\tb=R7RFSa7Rbck5j2lqoCQdt8anQjWGTRN/vwHvgTr8LH03pZyoiZ8Q8HbctCNpDkQB\tIZNQYlJUSkUvjmhuQpHXbYaYVYIe0IV2EKg0waaytVRuKlKrvgT+PsvPg9RHU2dMpAt\tvnYsI+0EPgNaFLiu+uN2DcJYzHaNsCRydqF/TBN0="}, {"Name": "MIME-Version", "Value": "1.0"}, {"Name": "Message-ID", "Value": "<<EMAIL>>"}, {"Name": "Feedback-ID", "Value": "::1.us-east-1.3t7I6SwltW62qGhmdPutNqFyMVZRfSQqBKPpUAGuXJU=:AmazonSES"}, {"Name": "X-SES-Outgoing", "Value": "2025.08.05-***********"}], "Attachments": []}